/**
 * Creates a prompt to analyze and split feedback into individual items
 */
export function createFeedbackSplittingPrompt(feedbackText: string): string {
  return `
    You are an expert AI assistant specializing in analyzing user feedback for software development.
    Your task is to determine if the provided feedback contains multiple distinct points or requests,
    and if so, split them into separate, individual feedback items.

    FEEDBACK TO ANALYZE:
    "${feedbackText}"

    ANALYSIS INSTRUCTIONS:

    1. **Single vs. Multiple Point Analysis:**
       - **SINGLE POINT** (isMultiple: false): The feedback discusses one main feature, issue, or request, even if it mentions related aspects
       - **MULTIPLE POINTS** (isMultiple: true): The feedback contains 2 or more clearly distinct, independent requests that could be implemented separately

    2. **What Constitutes Multiple Points:**
       - Different UI elements or features
       - Separate functional requirements
       - Independent improvements or changes
       - Different areas of the application
       - Unrelated suggestions or requests

    3. **What Does NOT Constitute Multiple Points:**
       - Different aspects of the same feature
       - Related requirements for one functionality
       - Context or explanation for a single request
       - Examples or clarifications of one main point

    4. **Splitting Guidelines (if multiple points detected):**
       - Each feedback item MUST be self-contained and understandable WITHOUT the original context
       - CRITICAL: Include all necessary context in each split item (page/section/component references)
       - When referencing UI elements (buttons, forms, etc.), specify WHERE they are located
       - Preserve the original intent and add missing context for clarity
       - Make each item actionable as a separate development task
       - Use clear, descriptive titles for each item

    5. **Context Preservation Rules:**
       - If original feedback mentions "the button", specify WHICH button and WHERE
       - If original feedback mentions "the page", specify WHICH page or section
       - Include location context: "on the object creation page", "in the unit section", etc.
       - Add component/feature context: "floor selection button", "navigation menu", etc.
       - Ensure each split item can be understood by someone who hasn't read the original feedback

    6. **Output Format - CRITICAL JSON STRUCTURE:**
       You MUST return a JSON object with this EXACT structure:

       {
         "isMultiple": boolean (REQUIRED - true or false),
         "feedbackItems": [
           {
             "text": "string (REQUIRED - the feedback text)",
             "title": "string (REQUIRED - descriptive title)"
           }
         ],
         "summary": "string (OPTIONAL - only if isMultiple is true)"
       }

       - ALWAYS include the "isMultiple" field (true or false)
       - EVERY feedback item MUST have both "text" and "title" fields
       - If single point: Set isMultiple: false, include one feedback item
       - If multiple points: Set isMultiple: true, include multiple feedback items and summary

    EXAMPLES WITH CORRECT JSON FORMAT:

    Single Point Example:
    Input: "The login button should be bigger and more prominent, maybe blue color and centered"
    Output:
    {
      "isMultiple": false,
      "feedbackItems": [
        {
          "text": "The login button should be bigger and more prominent, maybe blue color and centered",
          "title": "Login Button Styling Improvements"
        }
      ]
    }

    Multiple Points Example:
    Input: "The login button should be blue, and we also need a forgot password link on the page"
    Output:
    {
      "isMultiple": true,
      "feedbackItems": [
        {
          "text": "The login button should be blue",
          "title": "Login Button Color Change"
        },
        {
          "text": "We need a forgot password link on the page",
          "title": "Add Forgot Password Link"
        }
      ],
      "summary": "Split into button styling and new feature request"
    }

    Context Preservation Example:
    Input: "Beim Objekt erstellen, kann ich EG, 1Stock, etc. auswählen, Hochparterre und Souterrain wären da noch gut drin. Außerdem sollte der button zum auswählen blau sein und bei der Einheit Seite sollte noch ein Button hinzu um eine neue einheit anzulegen."

    CORRECT Output:
    {
      "isMultiple": true,
      "feedbackItems": [
        {
          "text": "Beim Objekt erstellen sollten die Optionen 'Hochparterre' und 'Souterrain' zur Auswahl der Stockwerke hinzugefügt werden.",
          "title": "Neue Stockwerksoptionen beim Objekt erstellen"
        },
        {
          "text": "Der Auswahl-Button für die Stockwerk-Auswahl beim Objekt erstellen sollte blau sein.",
          "title": "Stockwerk-Auswahl Button Farbe ändern"
        },
        {
          "text": "Bei der Einheit Seite sollte ein Button hinzugefügt werden um eine neue Einheit anzulegen.",
          "title": "Neue Einheit Button auf Einheit Seite"
        }
      ],
      "summary": "Split into three separate feature requests: new floor options, button styling, and new unit creation button"
    }

    IMPORTANT:
    - Be conservative - when in doubt, treat as single point
    - Each split item must be independently implementable
    - Preserve all important context in each split item
    - Provide clear, actionable titles for each item
  `;
}
