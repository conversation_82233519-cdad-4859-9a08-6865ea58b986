"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackSplittingSchema = void 0;
const genkit_1 = require("genkit");
// Schema for individual feedback item
const FeedbackItem = genkit_1.z.object({
    text: genkit_1.z.string().describe("Individual feedback text that represents a single, distinct point or request"),
    title: genkit_1.z.string().describe("Brief descriptive title for this feedback item"),
});
// Schema for feedback splitting response
exports.FeedbackSplittingSchema = genkit_1.z.object({
    isMultiple: genkit_1.z.boolean().describe("REQUIRED: Whether the original feedback contained multiple distinct points. Must be true or false."),
    feedbackItems: genkit_1.z.array(FeedbackItem).min(1).describe("REQUIRED: Array of individual feedback items. Each item must have both 'text' and 'title' fields."),
    summary: genkit_1.z.string().optional().describe("Brief explanation of how the feedback was split (only if isMultiple is true)"),
});
//# sourceMappingURL=feedback-splitting-schema.js.map